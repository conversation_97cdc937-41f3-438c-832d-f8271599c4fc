import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { Activity, Calendar, DollarSign, Users, Package, Tag, TrendingUp, MapPin, Star, Award } from "lucide-react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import AdminOverviewChart from "@/components/admin/admin-overview-chart"
import AdminRecentBookings from "@/components/admin/admin-recent-bookings"
import AdminUpcomingEvents from "@/components/admin/admin-upcoming-events"
import AdminAddonAnalytics from "@/components/admin/admin-addon-analytics"
import AdminPromoAnalytics from "@/components/admin/admin-promo-analytics"
import EnhancedKPICard, { KPIData } from "@/components/admin/enhanced-kpi-card"
import QuickActions from "@/components/admin/quick-actions"

export default function AdminDashboard() {
  // Enhanced KPI data with better structure
  const kpiData: KPIData[] = [
    {
      title: "Total Revenue",
      value: "₹27,00,000",
      change: { value: 35.5, period: "last month", type: "increase" },
      icon: <DollarSign className="h-5 w-5" />,
      description: "Monthly recurring revenue",
      color: "success",
      href: "/admin/payments",
    },
    {
      title: "Active Bookings",
      value: "1,547",
      change: { value: 12.3, period: "last week", type: "increase" },
      icon: <Calendar className="h-5 w-5" />,
      description: "Confirmed registrations",
      color: "info",
      href: "/admin/bookings",
      subtitle: "Across 21 cities"
    },
    {
      title: "Baby Games",
      value: "16",
      change: { value: 2, period: "this quarter", type: "increase" },
      icon: <Users className="h-5 w-5" />,
      description: "Active game categories",
      color: "default",
      href: "/admin/games",
    },
    {
      title: "Cities Active",
      value: "21",
      change: { value: 5.2, period: "this month", type: "increase" },
      icon: <MapPin className="h-5 w-5" />,
      description: "Hosting NIBOG events",
      color: "warning",
      href: "/admin/cities",
    },
    {
      title: "Customer Satisfaction",
      value: "4.8/5",
      change: { value: 0.3, period: "last month", type: "increase" },
      icon: <Star className="h-5 w-5" />,
      description: "Average rating",
      color: "success",
      href: "/admin/testimonials",
    },
    {
      title: "Completion Rate",
      value: "94.2%",
      change: { value: 2.1, period: "last month", type: "increase" },
      icon: <Award className="h-5 w-5" />,
      description: "Event completion rate",
      color: "info",
      href: "/admin/completed-events",
    },
  ]

  return (
    <div className="space-y-8">
      {/* Page Header - now handled by AdminHeader component */}

      {/* Enhanced KPI Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
        {kpiData.map((kpi, index) => (
          <div key={index} className={index >= 4 ? "xl:col-span-3" : ""}>
            <EnhancedKPICard data={kpi} />
          </div>
        ))}
      </div>

      {/* Quick Actions Section */}
      <QuickActions />

      {/* Enhanced Analytics Dashboard */}
      <div className="space-y-6">
        <Tabs defaultValue="overview" className="space-y-6">
          <div className="flex items-center justify-between">
            <TabsList className="grid w-full max-w-2xl grid-cols-5 h-11">
              <TabsTrigger value="overview" className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4" />
                <span className="hidden sm:inline">Overview</span>
              </TabsTrigger>
              <TabsTrigger value="bookings" className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                <span className="hidden sm:inline">Bookings</span>
              </TabsTrigger>
              <TabsTrigger value="events" className="flex items-center gap-2">
                <Activity className="h-4 w-4" />
                <span className="hidden sm:inline">Events</span>
              </TabsTrigger>
              <TabsTrigger value="addons" className="flex items-center gap-2">
                <Package className="h-4 w-4" />
                <span className="hidden sm:inline">Add-ons</span>
              </TabsTrigger>
              <TabsTrigger value="promos" className="flex items-center gap-2">
                <Tag className="h-4 w-4" />
                <span className="hidden sm:inline">Promos</span>
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid gap-6 lg:grid-cols-2">
              <Card className="lg:col-span-2">
                <CardHeader className="flex flex-row items-center justify-between">
                  <div>
                    <CardTitle className="text-xl">Revenue Overview</CardTitle>
                    <p className="text-sm text-muted-foreground mt-1">
                      Monthly revenue and booking trends
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="flex items-center gap-2 text-sm">
                      <div className="w-3 h-3 rounded-full bg-primary"></div>
                      <span>Revenue</span>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pl-2">
                  <AdminOverviewChart />
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="bookings" className="space-y-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle className="text-xl">Recent Bookings</CardTitle>
                  <p className="text-sm text-muted-foreground mt-1">
                    Latest event registrations and their status
                  </p>
                </div>
              </CardHeader>
              <CardContent>
                <AdminRecentBookings />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="events" className="space-y-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle className="text-xl">Upcoming Events</CardTitle>
                  <p className="text-sm text-muted-foreground mt-1">
                    Scheduled events and their booking status
                  </p>
                </div>
              </CardHeader>
              <CardContent>
                <AdminUpcomingEvents />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="addons" className="space-y-6">
            <AdminAddonAnalytics />
          </TabsContent>

          <TabsContent value="promos" className="space-y-6">
            <AdminPromoAnalytics />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

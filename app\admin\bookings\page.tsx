"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Eye, Edit, Check, X, AlertTriangle, Loader2, RefreshCw, CheckCircle, Mail, Phone } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { getAllBookings, getPaginatedBookings, updateBookingStatus, Booking, PaginatedBookingsResponse } from "@/services/bookingService"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import EnhancedDataTable, { Column, TableAction, BulkAction } from "@/components/admin/enhanced-data-table"
import { createBookingExportColumns } from "@/lib/export-utils"
import { EmptyBookings, EmptyError } from "@/components/ui/empty-state"
import { SkeletonTable } from "@/components/ui/skeleton-loader"

// Booking statuses for filtering
const statusOptions = [
  { id: "1", name: "Confirmed", value: "confirmed" },
  { id: "2", name: "Pending", value: "pending" },
  { id: "3", name: "Cancelled", value: "cancelled" },
  { id: "4", name: "Completed", value: "completed" },
  { id: "5", name: "No Show", value: "no show" },
  { id: "6", name: "Refunded", value: "refunded" },
]

// Booking statuses
const statuses = [
  { id: "1", name: "Confirmed" },
  { id: "2", name: "Pending" },
  { id: "3", name: "Cancelled" },
  { id: "4", name: "Completed" },
  { id: "5", name: "No Show" },
  { id: "6", name: "Refunded" },
]

const getStatusBadge = (status: string) => {
  switch (status.toLowerCase()) {
    case "confirmed":
      return <Badge className="bg-green-500 hover:bg-green-600">Confirmed</Badge>
    case "pending":
      return <Badge className="bg-yellow-500 hover:bg-yellow-600">Pending</Badge>
    case "cancelled":
      return <Badge className="bg-red-500 hover:bg-red-600">Cancelled</Badge>
    case "completed":
      return <Badge className="bg-blue-500 hover:bg-blue-600">Completed</Badge>
    case "no show":
      return <Badge className="bg-gray-500 hover:bg-gray-600">No Show</Badge>
    case "refunded":
      return <Badge className="bg-purple-500 hover:bg-purple-600">Refunded</Badge>
    default:
      return <Badge variant="outline">{status}</Badge>
  }
}

const getStatusBadge = (status: string) => {
  switch (status.toLowerCase()) {
    case "confirmed":
      return <Badge className="bg-green-500 hover:bg-green-600">Confirmed</Badge>
    case "pending":
      return <Badge className="bg-yellow-500 hover:bg-yellow-600">Pending</Badge>
    case "cancelled":
      return <Badge className="bg-red-500 hover:bg-red-600">Cancelled</Badge>
    case "completed":
      return <Badge className="bg-blue-500 hover:bg-blue-600">Completed</Badge>
    case "no show":
      return <Badge className="bg-gray-500 hover:bg-gray-600">No Show</Badge>
    case "refunded":
      return <Badge className="bg-purple-500 hover:bg-purple-600">Refunded</Badge>
    default:
      return <Badge variant="outline">{status}</Badge>
  }
}

export default function BookingsPage() {
  const { toast } = useToast()
  const [bookings, setBookings] = useState<Booking[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isProcessing, setIsProcessing] = useState<number | null>(null)

  // Fetch bookings from API
  const fetchBookings = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const response = await getPaginatedBookings({ page: 1, limit: 1000 })
      // Filter out empty booking objects
      const validBookings = (response.data || []).filter(booking => {
        return booking && Object.keys(booking).length > 0 &&
          Object.values(booking).some(value => value !== null && value !== undefined && value !== '')
      })
      setBookings(validBookings)
    } catch (error: any) {
      console.error("Failed to fetch bookings:", error)
      setError(error.message || "Failed to load bookings. Please try again.")
      toast({
        title: "Error",
        description: "Failed to load bookings. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchBookings()
  }, [])

  // Define table columns
  const columns: Column<Booking>[] = [
    {
      key: 'booking_id',
      label: 'Booking ID',
      sortable: true,
      width: '100px',
      render: (value) => <span className="font-mono text-sm">#{value}</span>
    },
    {
      key: 'parent_name',
      label: 'Parent',
      sortable: true,
      render: (value, row) => (
        <div>
          <div className="font-medium">{value}</div>
          <div className="text-xs text-muted-foreground">{row.parent_email}</div>
          {row.parent_additional_phone && (
            <div className="text-xs text-muted-foreground flex items-center gap-1">
              <Phone className="h-3 w-3" />
              {row.parent_additional_phone}
            </div>
          )}
        </div>
      )
    },
    {
      key: 'child_full_name',
      label: 'Child',
      sortable: true,
      render: (value, row) => (
        <div>
          <div className="font-medium">{value}</div>
          {row.child_age && (
            <div className="text-xs text-muted-foreground">Age: {row.child_age}</div>
          )}
        </div>
      )
    },
    {
      key: 'event_title',
      label: 'Event',
      sortable: true,
      render: (value, row) => (
        <div>
          <div className="font-medium">{value}</div>
          <div className="text-xs text-muted-foreground">{row.city_name}</div>
          <div className="text-xs text-muted-foreground">{row.venue_name}</div>
        </div>
      )
    },
    {
      key: 'total_amount',
      label: 'Amount',
      sortable: true,
      align: 'right',
      width: '100px',
      render: (value) => <span className="font-medium">₹{value}</span>
    },
    {
      key: 'booking_status',
      label: 'Status',
      sortable: true,
      width: '120px',
      render: (value) => getStatusBadge(value)
    },
    {
      key: 'booking_created_at',
      label: 'Booking Date',
      sortable: true,
      width: '120px',
      render: (value) => new Date(value).toLocaleDateString()
    },
  ]

  // Handle confirm booking
  const handleConfirmBooking = async (booking: Booking) => {
    try {
      setIsProcessing(booking.booking_id)
      await updateBookingStatus(booking.booking_id, "Confirmed")

      setBookings(bookings.map(b =>
        b.booking_id === booking.booking_id ? { ...b, booking_status: "Confirmed" } : b
      ))

      toast({
        title: "Success",
        description: `Booking #${booking.booking_id} has been confirmed.`,
      })
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to confirm booking.",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(null)
    }
  }

  // Handle cancel booking
  const handleCancelBooking = async (booking: Booking) => {
    try {
      setIsProcessing(booking.booking_id)
      await updateBookingStatus(booking.booking_id, "Cancelled")

      setBookings(bookings.map(b =>
        b.booking_id === booking.booking_id ? { ...b, booking_status: "Cancelled" } : b
      ))

      toast({
        title: "Success",
        description: `Booking #${booking.booking_id} has been cancelled.`,
      })
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to cancel booking.",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(null)
    }
  }

  // Define table actions
  const actions: TableAction<Booking>[] = [
    {
      label: "View Details",
      icon: <Eye className="h-4 w-4 mr-2" />,
      onClick: (booking) => window.open(`/admin/bookings/${booking.booking_id}`, '_blank'),
    },
    {
      label: "Edit",
      icon: <Edit className="h-4 w-4 mr-2" />,
      onClick: (booking) => window.open(`/admin/bookings/${booking.booking_id}/edit`, '_blank'),
    },
    {
      label: "Confirm",
      icon: <Check className="h-4 w-4 mr-2" />,
      onClick: handleConfirmBooking,
      disabled: (booking) => booking.booking_status?.toLowerCase() !== 'pending',
    },
    {
      label: "Cancel",
      icon: <X className="h-4 w-4 mr-2" />,
      onClick: handleCancelBooking,
      variant: "destructive",
      disabled: (booking) => !['pending', 'confirmed'].includes(booking.booking_status?.toLowerCase() || ''),
    },
  ]

  // Define bulk actions
  const bulkActions: BulkAction<Booking>[] = [
    {
      label: "Confirm Selected",
      icon: <CheckCircle className="h-4 w-4 mr-2" />,
      onClick: async (selectedBookings) => {
        const pendingBookings = selectedBookings.filter(b => b.booking_status?.toLowerCase() === 'pending')
        if (pendingBookings.length === 0) {
          toast({
            title: "No Action Needed",
            description: "No pending bookings selected.",
          })
          return
        }

        try {
          await Promise.all(pendingBookings.map(booking =>
            updateBookingStatus(booking.booking_id, "Confirmed")
          ))

          setBookings(bookings.map(booking =>
            pendingBookings.some(pb => pb.booking_id === booking.booking_id)
              ? { ...booking, booking_status: "Confirmed" }
              : booking
          ))

          toast({
            title: "Success",
            description: `${pendingBookings.length} bookings confirmed.`,
          })
        } catch (error: any) {
          toast({
            title: "Error",
            description: "Failed to confirm some bookings.",
            variant: "destructive",
          })
        }
      },
    },
    {
      label: "Send Email",
      icon: <Mail className="h-4 w-4 mr-2" />,
      onClick: (selectedBookings) => {
        // Implement bulk email functionality
        toast({
          title: "Feature Coming Soon",
          description: "Bulk email functionality will be available soon.",
        })
      },
    },
  ]



  // Calculate summary statistics
  const confirmedBookings = bookings.filter(b => b.booking_status?.toLowerCase() === 'confirmed').length
  const pendingBookings = bookings.filter(b => b.booking_status?.toLowerCase() === 'pending').length
  const cancelledBookings = bookings.filter(b => b.booking_status?.toLowerCase() === 'cancelled').length
  const completedBookings = bookings.filter(b => b.booking_status?.toLowerCase() === 'completed').length
  const totalRevenue = bookings
    .filter(b => ['confirmed', 'completed'].includes(b.booking_status?.toLowerCase() || ''))
    .reduce((sum, b) => sum + parseFloat(b.total_amount || '0'), 0)

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Bookings</h1>
            <p className="text-muted-foreground">Manage NIBOG event bookings</p>
          </div>
        </div>
        <EmptyError onRetry={fetchBookings} error={error} />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Bookings</h1>
          <p className="text-muted-foreground">Manage NIBOG event bookings</p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={fetchBookings}
            disabled={isLoading}
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button asChild>
            <Link href="/admin/bookings/new">
              <Eye className="mr-2 h-4 w-4" />
              Create New Booking
            </Link>
          </Button>
        </div>
      </div>

      {/* Summary Statistics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold">{bookings.length}</div>
            <p className="text-xs text-muted-foreground">Total Bookings</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-green-600">{confirmedBookings}</div>
            <p className="text-xs text-muted-foreground">Confirmed</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-yellow-600">{pendingBookings}</div>
            <p className="text-xs text-muted-foreground">Pending</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-red-600">{cancelledBookings}</div>
            <p className="text-xs text-muted-foreground">Cancelled</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold">₹{totalRevenue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Total Revenue</p>
          </CardContent>
        </Card>
      </div>

      {/* Enhanced Data Table */}
      <EnhancedDataTable
        data={bookings}
        columns={columns}
        actions={actions}
        bulkActions={bulkActions}
        loading={isLoading}
        searchable={true}
        filterable={true}
        exportable={true}
        selectable={true}
        pagination={true}
        pageSize={25}
        exportColumns={createBookingExportColumns()}
        exportTitle="NIBOG Bookings Report"
        exportFilename="nibog-bookings"
        emptyMessage="No bookings found"
        onRefresh={fetchBookings}
        className="min-h-[400px]"
      />
    </div>
  )
}
          <TableHeader>
            <TableRow>
              <TableHead>Booking ID</TableHead>
              <TableHead>Parent</TableHead>
              <TableHead>Child</TableHead>
              <TableHead>Event</TableHead>
              <TableHead>City</TableHead>
              <TableHead>Booking Date</TableHead>
              <TableHead>Amount</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={9} className="h-24 text-center">
                  <Loader2 className="h-8 w-8 animate-spin mx-auto" />
                  <div className="mt-2">Loading bookings...</div>
                </TableCell>
              </TableRow>
            ) : error ? (
              <TableRow>
                <TableCell colSpan={9} className="h-24 text-center text-destructive">
                  <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
                  {error}
                </TableCell>
              </TableRow>
            ) : filteredBookings.length === 0 ? (
              <TableRow>
                <TableCell colSpan={9} className="h-24 text-center">
                  No bookings found.
                </TableCell>
              </TableRow>
            ) : (
              (() => {
                const validBookings = filteredBookings
                  .filter(
                    booking =>
                      booking.booking_id &&
                      booking.parent_name &&
                      booking.child_full_name &&
                      booking.event_title &&
                      booking.city_name &&
                      booking.total_amount
                  );
                if (validBookings.length === 0) {
                  return (
                    <TableRow>
                      <TableCell colSpan={9} className="h-24 text-center">
                        No bookings found.
                      </TableCell>
                    </TableRow>
                  );
                }
                return validBookings.map((booking, idx) => (
                  <TableRow key={booking.booking_id ?? idx}>
                    <TableCell className="font-medium">{booking.booking_id}</TableCell>
                    <TableCell>
                      {booking.parent_name}
                      <div className="text-xs text-muted-foreground">{booking.parent_email}</div>
                    </TableCell>
                    <TableCell>
                      {booking.child_full_name}
                    </TableCell>
                    <TableCell>
                      {booking.event_title}
                      <div className="text-xs text-muted-foreground">
                        Booked: {booking.booking_created_at ? new Date(booking.booking_created_at).toLocaleDateString() : 'N/A'}
                      </div>
                    </TableCell>
                    <TableCell>{booking.city_name}</TableCell>
                    <TableCell>
                      <div>{booking.child_full_name}</div>
                      <div className="text-xs text-muted-foreground">
                        Booked: {booking.booking_created_at ? new Date(booking.booking_created_at).toLocaleDateString() : 'N/A'}
                      </div>
                    </TableCell>
                    <TableCell>₹{booking.total_amount}</TableCell>
                    <TableCell>{getStatusBadge(typeof booking.booking_status === "string" ? booking.booking_status.toLowerCase() : "")}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button variant="ghost" size="icon" asChild>
                          <Link href={`/admin/bookings/${booking.booking_id}`}>
                            <Eye className="h-4 w-4" />
                            <span className="sr-only">View</span>
                          </Link>
                        </Button>
                        <Button variant="ghost" size="icon" asChild>
                          <Link href={`/admin/bookings/${booking.booking_id}/edit`}>
                            <Edit className="h-4 w-4" />
                            <span className="sr-only">Edit</span>
                          </Link>
                        </Button>
                        {typeof booking.booking_status === "string" && booking.booking_status.toLowerCase() === "pending" && (
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                disabled={isProcessing === booking.booking_id}
                              >
                                <Check className="h-4 w-4" />
                                <span className="sr-only">Confirm</span>
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Confirm Booking</AlertDialogTitle>
                                <AlertDialogDescription>
                                  <div className="flex items-start gap-2">
                                    <CheckCircle className="mt-0.5 h-5 w-5 text-green-500" />
                                    <div className="space-y-2">
                                      <div className="font-medium">Do you want to confirm this booking?</div>
                                      <div>
                                        This will confirm booking #{booking.booking_id} for {booking.parent_name} for the {booking.event_title} event.
                                        The booking status will change from "Pending" to "Confirmed".
                                      </div>
                                    </div>
                                  </div>
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>No, Keep Pending</AlertDialogCancel>
                                <AlertDialogAction
                                  className="bg-green-500 hover:bg-green-600"
                                  onClick={() => handleConfirmBooking(booking.booking_id)}
                                  disabled={isProcessing === booking.booking_id}
                                >
                                  {isProcessing === booking.booking_id ? (
                                    <>
                                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                      Confirming...
                                    </>
                                  ) : "Yes, Confirm Booking"}
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        )}
                        {typeof booking.booking_status === "string" && booking.booking_status.toLowerCase() === "confirmed" && (
                          <>
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button variant="ghost" size="icon">
                                  <X className="h-4 w-4" />
                                  <span className="sr-only">Cancel</span>
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Cancel Booking</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    <div className="flex items-start gap-2">
                                      <AlertTriangle className="mt-0.5 h-5 w-5 text-amber-500" />
                                      <div className="space-y-2">
                                        <div className="font-medium">Are you sure you want to cancel this booking?</div>
                                        <div>
                                          This will cancel booking {booking.booking_id} for {booking.parent_name} for the {booking.event_title} event.
                                          The user will be notified and may be eligible for a refund.
                                        </div>
                                      </div>
                                    </div>
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>No, Keep Booking</AlertDialogCancel>
                                  <AlertDialogAction
                                    className="bg-red-500 hover:bg-red-600"
                                    onClick={() => handleCancelBooking(booking.booking_id)}
                                    disabled={isProcessing === booking.booking_id}
                                  >
                                    {isProcessing === booking.booking_id ? (
                                      <>
                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                        Cancelling...
                                      </>
                                    ) : "Yes, Cancel Booking"}
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              })()
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination Controls */}
      <div className="flex items-center justify-between px-2 py-4">
        <div className="flex items-center space-x-2">
          <p className="text-sm text-muted-foreground">
            {filteredBookings.length === 0
              ? "No bookings found."
              : `Showing ${bookings.length > 0 ? ((currentPage - 1) * pageSize) + 1 : 0} to ${Math.min(currentPage * pageSize, totalBookings)} of ${totalBookings} bookings`}
          </p>
        </div>

        <div className="flex items-center space-x-6">
          {/* Page Size Selector */}
          <div className="flex items-center space-x-2">
            <p className="text-sm font-medium">Rows per page</p>
            <Select
              value={pageSize.toString()}
              onValueChange={(value) => handlePageSizeChange(parseInt(value))}
            >
              <SelectTrigger className="h-8 w-[70px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent side="top">
                <SelectItem value="25">25</SelectItem>
                <SelectItem value="50">50</SelectItem>
                <SelectItem value="100">100</SelectItem>
                <SelectItem value="200">200</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Page Navigation */}
          <div className="flex items-center space-x-2">
            <p className="text-sm font-medium">
              Page {currentPage} of {totalPages}
            </p>
            <div className="flex items-center space-x-1">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(1)}
                disabled={!hasPrev || isLoading}
              >
                First
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={!hasPrev || isLoading}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={!hasNext || isLoading}
              >
                Next
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(totalPages)}
                disabled={!hasNext || isLoading}
              >
                Last
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
